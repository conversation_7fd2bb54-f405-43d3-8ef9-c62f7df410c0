-- =============================================
-- Authentication Management Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- sp_Auth_Login - Authenticate user and get user details
-- =============================================
IF OBJECT_ID('sp_Auth_Login', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_Login;
GO

CREATE PROCEDURE sp_Auth_Login
    @username NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.user_id,
        u.username,
        u.password_hash,
        u.full_name,
        u.full_name_urdu,
        u.email,
        u.phone,
        u.role_id,
        r.role_name,
        u.is_active,
        u.last_login,
        u.created_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.username = @username;
END;
GO

-- =============================================
-- sp_Auth_UpdateLastLogin - Update user's last login time
-- =============================================
IF OBJECT_ID('sp_Auth_UpdateLastLogin', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_UpdateLastLogin;
GO

CREATE PROCEDURE sp_Auth_UpdateLastLogin
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE users
    SET last_login = GETDATE()
    WHERE user_id = @user_id;
END;
GO

-- =============================================
-- sp_Auth_GetCurrentUser - Get current user details by user_id
-- =============================================
IF OBJECT_ID('sp_Auth_GetCurrentUser', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_GetCurrentUser;
GO

CREATE PROCEDURE sp_Auth_GetCurrentUser
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.user_id,
        u.username,
        u.email,
        u.full_name,
        u.full_name_urdu,
        u.role_id,
        r.role_name,
        u.phone,
        u.is_active,
        u.created_at,
        u.updated_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.user_id = @user_id;
END;
GO

-- =============================================
-- sp_Auth_ChangePassword - Change user password
-- =============================================
IF OBJECT_ID('sp_Auth_ChangePassword', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_ChangePassword;
GO

CREATE PROCEDURE sp_Auth_ChangePassword
    @user_id INT,
    @new_password_hash NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE users
    SET password_hash = @new_password_hash,
        updated_at = GETDATE()
    WHERE user_id = @user_id;
    
    SELECT @@ROWCOUNT AS rows_affected;
END;
GO

-- =============================================
-- sp_Auth_GetPasswordHash - Get user's password hash for verification
-- =============================================
IF OBJECT_ID('sp_Auth_GetPasswordHash', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_GetPasswordHash;
GO

CREATE PROCEDURE sp_Auth_GetPasswordHash
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT password_hash
    FROM users
    WHERE user_id = @user_id;
END;
GO

-- =============================================
-- sp_Auth_GetAllRoles - Get all active roles
-- =============================================
IF OBJECT_ID('sp_Auth_GetAllRoles', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_GetAllRoles;
GO

CREATE PROCEDURE sp_Auth_GetAllRoles
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT *
    FROM roles
    WHERE is_active = 1
    ORDER BY role_name;
END;
GO

PRINT 'Authentication stored procedures created successfully!';
GO

