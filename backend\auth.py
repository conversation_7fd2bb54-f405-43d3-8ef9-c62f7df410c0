"""
Authentication and authorization utilities
"""
from datetime import datetime, timedelta
from typing import Op<PERSON>, Dict
from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from config import get_settings
from database import execute_query, execute_scalar, execute_non_query
import logging

logger = logging.getLogger(__name__)
settings = get_settings()

# Password hashing - use bcrypt directly to avoid passlib issues
import bcrypt as bcrypt_lib

# JWT Bearer token
security = HTTPBearer()


def hash_password(password: str) -> str:
    """Hash a password using bcrypt"""
    # Truncate password to 72 bytes for bcrypt
    password_bytes = password.encode('utf-8')[:72]
    salt = bcrypt_lib.gensalt()
    hashed = bcrypt_lib.hashpw(password_bytes, salt)
    return hashed.decode('utf-8')


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    try:
        # Truncate password to 72 bytes for bcrypt
        password_bytes = plain_password.encode('utf-8')[:72]
        hashed_bytes = hashed_password.encode('utf-8')
        return bcrypt_lib.checkpw(password_bytes, hashed_bytes)
    except Exception as e:
        logger.error(f"Password verification error: {e}")
        return False


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token
    
    Args:
        data: Dictionary with user data to encode
        expires_delta: Token expiration time
    
    Returns:
        Encoded JWT token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    
    return encoded_jwt


def create_refresh_token(data: dict) -> str:
    """
    Create JWT refresh token
    
    Args:
        data: Dictionary with user data to encode
    
    Returns:
        Encoded JWT refresh token
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt


def decode_token(token: str) -> Dict:
    """
    Decode and validate JWT token
    
    Args:
        token: JWT token string
    
    Returns:
        Decoded token payload
    
    Raises:
        HTTPException: If token is invalid or expired
    """
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        return payload
    except JWTError as e:
        logger.error(f"JWT decode error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def authenticate_user(username: str, password: str) -> Optional[Dict]:
    """
    Authenticate user with username and password

    Args:
        username: User's username
        password: User's plain password

    Returns:
        User dictionary if authenticated, None otherwise
    """
    # Use stored procedure to get user details
    query = "EXEC sp_Auth_Login @username = ?"

    results = execute_query(query, (username,))

    if not results:
        return None

    user = results[0]

    if not user['is_active']:
        return None

    if not verify_password(password, user['password_hash']):
        return None
    
    # Update last login using stored procedure
    update_query = "EXEC sp_Auth_UpdateLastLogin @user_id = ?"
    execute_non_query(update_query, (user['user_id'],))

    return user


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict:
    """
    Get current authenticated user from JWT token

    Args:
        credentials: HTTP Bearer credentials

    Returns:
        Current user dictionary

    Raises:
        HTTPException: If authentication fails
    """
    print(f"[AUTH] get_current_user called")
    token = credentials.credentials
    print(f"[AUTH] Token: {token[:50]}...")
    payload = decode_token(token)
    
    user_id: int = payload.get("user_id")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )
    
    # Get user from database
    query = """
        SELECT u.user_id, u.username, u.full_name, u.email, u.role_id, r.role_name, u.is_active
        FROM users u
        INNER JOIN roles r ON u.role_id = r.role_id
        WHERE u.user_id = ?
    """
    
    results = execute_query(query, (user_id,))

    if not results or not results[0]['is_active']:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive",
        )

    user_data = results[0]
    print(f"[AUTH] User authenticated: {user_data.get('username')} - Role: {user_data.get('role_name')}")
    logger.info(f"User authenticated: {user_data.get('username')} - Role: {user_data.get('role_name')}")
    return user_data


def require_role(allowed_roles: list):
    """
    Dependency to check if user has required role
    
    Args:
        allowed_roles: List of allowed role names
    
    Returns:
        Dependency function
    """
    async def role_checker(current_user: Dict = Depends(get_current_user)) -> Dict:
        if current_user['role_name'] not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {', '.join(allowed_roles)}",
            )
        return current_user
    
    return role_checker


# Role-based dependencies
async def require_admin(current_user: Dict = Depends(get_current_user)) -> Dict:
    """Require Administrator role"""
    if current_user['role_name'] != 'Administrator':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Administrator access required",
        )
    return current_user


async def require_teacher(current_user: Dict = Depends(get_current_user)) -> Dict:
    """Require Teacher or Administrator role"""
    if current_user['role_name'] not in ['Teacher', 'Administrator']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Teacher access required",
        )
    return current_user


async def require_accountant(current_user: Dict = Depends(get_current_user)) -> Dict:
    """Require Accountant or Administrator role"""
    if current_user['role_name'] not in ['Accountant', 'Administrator']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Accountant access required",
        )
    return current_user


async def require_receptionist(current_user: Dict = Depends(get_current_user)) -> Dict:
    """Require Receptionist or Administrator role"""
    if current_user['role_name'] not in ['Receptionist', 'Administrator']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Receptionist access required",
        )
    return current_user


def create_user(username: str, password: str, full_name: str, role_id: int, 
                email: Optional[str] = None, phone: Optional[str] = None) -> int:
    """
    Create a new user
    
    Args:
        username: User's username
        password: User's plain password
        full_name: User's full name
        role_id: Role ID
        email: User's email (optional)
        phone: User's phone (optional)
    
    Returns:
        New user ID
    """
    password_hash = hash_password(password)
    
    query = """
        INSERT INTO users (username, password_hash, full_name, email, phone, role_id)
        VALUES (?, ?, ?, ?, ?, ?);
        SELECT SCOPE_IDENTITY() AS user_id;
    """
    
    user_id = execute_scalar(query, (username, password_hash, full_name, email, phone, role_id))
    return int(user_id)


def change_password(user_id: int, old_password: str, new_password: str) -> bool:
    """
    Change user password
    
    Args:
        user_id: User ID
        old_password: Current password
        new_password: New password
    
    Returns:
        True if successful, False otherwise
    """
    # Verify old password
    query = "SELECT password_hash FROM users WHERE user_id = ?"
    result = execute_scalar(query, (user_id,))
    
    if not result or not verify_password(old_password, result):
        return False
    
    # Update password
    new_hash = hash_password(new_password)
    update_query = "UPDATE users SET password_hash = ?, updated_at = GETDATE() WHERE user_id = ?"
    execute_query(update_query, (new_hash, user_id))
    
    return True

